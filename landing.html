<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Narrate - Share Your Story</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, onAuthStateChanged, signOut } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc, getDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import firebaseConfig from './firebase-config.js';

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.firebaseAuth = auth;
        window.firebaseDb = db;
        window.firebaseAuthMethods = {
            createUserWithEmailAndPassword,
            signInWithEmailAndPassword,
            onAuthStateChanged,
            signOut,
            setDoc,
            getDoc,
            doc
        };
    </script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .lucide {
            width: 20px;
            height: 20px;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4f46e5;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-slate-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="book-open" class="w-5 h-5 text-white"></i>
                        </div>
                        <span class="ml-2 text-xl font-bold text-slate-900">Narrate</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="loginBtn" class="text-slate-600 hover:text-slate-900 font-medium">Sign In</button>
                    <button id="signupBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition">Get Started</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Share Your Story,<br>
                <span class="text-indigo-200">Connect Your Community</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-indigo-100 max-w-3xl mx-auto">
                Narrate is where authentic stories come to life. Join a community that values real connections, meaningful conversations, and the power of shared experiences.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button id="heroSignupBtn" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold text-lg hover:bg-indigo-50 transition">
                    Start Sharing Today
                </button>
                <button id="heroLoginBtn" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold text-lg hover:bg-white hover:text-indigo-600 transition">
                    Sign In
                </button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-4">Why Choose Narrate?</h2>
                <p class="text-xl text-slate-600 max-w-2xl mx-auto">
                    Built for storytellers, by storytellers. Experience social media that prioritizes authenticity over algorithms.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="feature-card bg-slate-50 p-8 rounded-xl text-center">
                    <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="heart" class="w-8 h-8 text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-4">Authentic Connections</h3>
                    <p class="text-slate-600">
                        Build genuine relationships through meaningful stories. No fake metrics, just real human connections.
                    </p>
                </div>
                
                <div class="feature-card bg-slate-50 p-8 rounded-xl text-center">
                    <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="users" class="w-8 h-8 text-emerald-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-4">Community First</h3>
                    <p class="text-slate-600">
                        Join communities that matter to you. Share experiences, support others, and grow together.
                    </p>
                </div>
                
                <div class="feature-card bg-slate-50 p-8 rounded-xl text-center">
                    <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="shield-check" class="w-8 h-8 text-amber-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-4">Safe & Respectful</h3>
                    <p class="text-slate-600">
                        A platform designed with safety in mind. Share your stories in a respectful, moderated environment.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-20 bg-slate-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-4">How It Works</h2>
                <p class="text-xl text-slate-600">
                    Getting started is simple. Share your story in three easy steps.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-12 h-12 bg-indigo-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold">1</div>
                    <h3 class="text-xl font-bold text-slate-900 mb-4">Create Your Account</h3>
                    <p class="text-slate-600">
                        Sign up with your email and create your unique profile. Tell us a bit about yourself.
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-indigo-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold">2</div>
                    <h3 class="text-xl font-bold text-slate-900 mb-4">Share Your Story</h3>
                    <p class="text-slate-600">
                        Write about your experiences, thoughts, and moments that matter. Add photos to bring your stories to life.
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-indigo-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold">3</div>
                    <h3 class="text-xl font-bold text-slate-900 mb-4">Connect & Engage</h3>
                    <p class="text-slate-600">
                        Discover other storytellers, engage with their content, and build meaningful connections.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-indigo-600 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Share Your Story?</h2>
            <p class="text-xl mb-8 text-indigo-100">
                Join thousands of storytellers who have found their voice on Narrate. Your story matters.
            </p>
            <button id="ctaSignupBtn" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold text-lg hover:bg-indigo-50 transition">
                Get Started for Free
            </button>
        </div>
    </section>

    <!-- Authentication Modal -->
    <div id="authModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-slate-200 flex justify-between items-center">
                <h3 id="authModalTitle" class="text-xl font-bold text-slate-900">Welcome to Narrate</h3>
                <button id="closeAuthModalBtn" class="p-1 rounded-full hover:bg-slate-100">
                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                </button>
            </div>

            <!-- Login Form -->
            <div id="loginForm" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label for="loginEmail" class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                        <input type="email" id="loginEmail" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Enter your email">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                        <input type="password" id="loginPassword" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Enter your password">
                    </div>
                    <button id="loginSubmitBtn" class="w-full bg-indigo-600 text-white py-2 rounded-lg font-medium hover:bg-indigo-700 transition">
                        Sign In
                    </button>
                </div>
                <div class="mt-4 text-center">
                    <p class="text-sm text-slate-600">
                        Don't have an account?
                        <button id="showSignupBtn" class="text-indigo-600 hover:text-indigo-700 font-medium">Sign up</button>
                    </p>
                </div>
            </div>

            <!-- Signup Form -->
            <div id="signupForm" class="hidden p-6">
                <div class="space-y-4">
                    <div>
                        <label for="signupUsername" class="block text-sm font-medium text-slate-700 mb-1">Username</label>
                        <input type="text" id="signupUsername" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Choose a username">
                    </div>
                    <div>
                        <label for="signupEmail" class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                        <input type="email" id="signupEmail" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Enter your email">
                    </div>
                    <div>
                        <label for="signupPassword" class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                        <input type="password" id="signupPassword" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Create a password">
                    </div>
                    <button id="signupSubmitBtn" class="w-full bg-indigo-600 text-white py-2 rounded-lg font-medium hover:bg-indigo-700 transition">
                        Create Account
                    </button>
                </div>
                <div class="mt-4 text-center">
                    <p class="text-sm text-slate-600">
                        Already have an account?
                        <button id="showLoginBtn" class="text-indigo-600 hover:text-indigo-700 font-medium">Sign in</button>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="book-open" class="w-5 h-5 text-white"></i>
                    </div>
                    <span class="ml-2 text-xl font-bold">Narrate</span>
                </div>
                <div class="text-slate-400">
                    <p>&copy; 2024 Narrate. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // DOM elements
        const authModal = document.getElementById('authModal');
        const closeAuthModalBtn = document.getElementById('closeAuthModalBtn');
        const loginForm = document.getElementById('loginForm');
        const signupForm = document.getElementById('signupForm');
        const authModalTitle = document.getElementById('authModalTitle');

        // Button elements
        const loginBtn = document.getElementById('loginBtn');
        const signupBtn = document.getElementById('signupBtn');
        const heroSignupBtn = document.getElementById('heroSignupBtn');
        const heroLoginBtn = document.getElementById('heroLoginBtn');
        const ctaSignupBtn = document.getElementById('ctaSignupBtn');
        const showSignupBtn = document.getElementById('showSignupBtn');
        const showLoginBtn = document.getElementById('showLoginBtn');
        const loginSubmitBtn = document.getElementById('loginSubmitBtn');
        const signupSubmitBtn = document.getElementById('signupSubmitBtn');

        // Modal functions
        function openAuthModal() {
            authModal.classList.remove('hidden');
        }

        function closeAuthModal() {
            authModal.classList.add('hidden');
            clearForms();
        }

        function showLoginForm() {
            loginForm.classList.remove('hidden');
            signupForm.classList.add('hidden');
            authModalTitle.textContent = 'Welcome Back';
        }

        function showSignupForm() {
            loginForm.classList.add('hidden');
            signupForm.classList.remove('hidden');
            authModalTitle.textContent = 'Join Narrate';
        }

        function clearForms() {
            document.getElementById('loginEmail').value = '';
            document.getElementById('loginPassword').value = '';
            document.getElementById('signupUsername').value = '';
            document.getElementById('signupEmail').value = '';
            document.getElementById('signupPassword').value = '';
        }

        function showLoading(button) {
            button.disabled = true;
            button.innerHTML = '<div class="spinner mx-auto"></div>';
        }

        function hideLoading(button, text) {
            button.disabled = false;
            button.innerHTML = text;
        }

        function showError(message) {
            alert(message); // You can replace this with a better notification system
        }

        function showSuccess(message) {
            alert(message); // You can replace this with a better notification system
        }

        // Event listeners
        loginBtn.addEventListener('click', () => {
            openAuthModal();
            showLoginForm();
        });

        signupBtn.addEventListener('click', () => {
            openAuthModal();
            showSignupForm();
        });

        heroSignupBtn.addEventListener('click', () => {
            openAuthModal();
            showSignupForm();
        });

        heroLoginBtn.addEventListener('click', () => {
            openAuthModal();
            showLoginForm();
        });

        ctaSignupBtn.addEventListener('click', () => {
            openAuthModal();
            showSignupForm();
        });

        showSignupBtn.addEventListener('click', showSignupForm);
        showLoginBtn.addEventListener('click', showLoginForm);
        closeAuthModalBtn.addEventListener('click', closeAuthModal);

        // Close modal when clicking outside
        authModal.addEventListener('click', (e) => {
            if (e.target === authModal) {
                closeAuthModal();
            }
        });

        // Firebase Authentication Functions
        async function handleLogin() {
            const email = document.getElementById('loginEmail').value.trim();
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                showError('Please fill in all fields');
                return;
            }

            showLoading(loginSubmitBtn);

            try {
                const userCredential = await window.firebaseAuthMethods.signInWithEmailAndPassword(window.firebaseAuth, email, password);
                const user = userCredential.user;

                showSuccess('Successfully signed in!');
                closeAuthModal();

                // Redirect to main app
                window.location.href = '/app';

            } catch (error) {
                console.error('Login error:', error);
                let errorMessage = 'Failed to sign in. Please try again.';

                switch (error.code) {
                    case 'auth/user-not-found':
                        errorMessage = 'No account found with this email.';
                        break;
                    case 'auth/wrong-password':
                        errorMessage = 'Incorrect password.';
                        break;
                    case 'auth/invalid-email':
                        errorMessage = 'Invalid email address.';
                        break;
                    case 'auth/too-many-requests':
                        errorMessage = 'Too many failed attempts. Please try again later.';
                        break;
                }

                showError(errorMessage);
            } finally {
                hideLoading(loginSubmitBtn, 'Sign In');
            }
        }

        async function handleSignup() {
            const username = document.getElementById('signupUsername').value.trim();
            const email = document.getElementById('signupEmail').value.trim();
            const password = document.getElementById('signupPassword').value;

            if (!username || !email || !password) {
                showError('Please fill in all fields');
                return;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long');
                return;
            }

            showLoading(signupSubmitBtn);

            try {
                const userCredential = await window.firebaseAuthMethods.createUserWithEmailAndPassword(window.firebaseAuth, email, password);
                const user = userCredential.user;

                // Create user profile in Firestore
                await window.firebaseAuthMethods.setDoc(window.firebaseAuthMethods.doc(window.firebaseDb, 'users', user.uid), {
                    username: username,
                    email: email,
                    bio: 'New to the community',
                    createdAt: new Date().toISOString(),
                    followers: 0,
                    following: 0,
                    stories: 0
                });

                showSuccess('Account created successfully!');
                closeAuthModal();

                // Redirect to main app
                window.location.href = '/app';

            } catch (error) {
                console.error('Signup error:', error);
                let errorMessage = 'Failed to create account. Please try again.';

                switch (error.code) {
                    case 'auth/email-already-in-use':
                        errorMessage = 'An account with this email already exists.';
                        break;
                    case 'auth/invalid-email':
                        errorMessage = 'Invalid email address.';
                        break;
                    case 'auth/weak-password':
                        errorMessage = 'Password is too weak. Please choose a stronger password.';
                        break;
                }

                showError(errorMessage);
            } finally {
                hideLoading(signupSubmitBtn, 'Create Account');
            }
        }

        // Form submission handlers
        loginSubmitBtn.addEventListener('click', handleLogin);
        signupSubmitBtn.addEventListener('click', handleSignup);

        // Handle Enter key in forms
        document.getElementById('loginPassword').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });

        document.getElementById('signupPassword').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSignup();
            }
        });

        // Check if user is already logged in
        if (window.firebaseAuthMethods && window.firebaseAuth) {
            window.firebaseAuthMethods.onAuthStateChanged(window.firebaseAuth, (user) => {
                if (user) {
                    // User is signed in, redirect to main app
                    window.location.href = '/app';
                }
            });
        }
    </script>
</body>
</html>
