<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Social Feed UI with Gemini Features</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Firebase SDK -->
    <script type="module">
        try {
            import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
            import { getAuth, onAuthStateChanged, signOut } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
            import { getFirestore, doc, getDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

            // Try to import Firebase config
            let firebaseConfig;
            try {
                const configModule = await import('./firebase-config.js');
                firebaseConfig = configModule.default;
            } catch (error) {
                console.warn('Firebase config not found or invalid, Firebase features will be disabled');
                window.firebaseConfigured = false;
                return;
            }

            // Check if config has placeholder values
            if (!firebaseConfig || firebaseConfig.apiKey === 'your-api-key-here' || !firebaseConfig.projectId || firebaseConfig.projectId === 'your-project-id') {
                console.warn('Firebase config contains placeholder values, Firebase features will be disabled');
                window.firebaseConfigured = false;
                return;
            }

            // Initialize Firebase
            const app = initializeApp(firebaseConfig);
            const auth = getAuth(app);
            const db = getFirestore(app);

            // Make Firebase available globally
            window.firebaseAuth = auth;
            window.firebaseDb = db;
            window.firebaseConfigured = true;
            window.firebaseAuthMethods = {
                onAuthStateChanged,
                signOut,
                getDoc,
                doc
            };
        } catch (error) {
            console.warn('Failed to initialize Firebase:', error);
            window.firebaseConfigured = false;
        }

        // Initialize Firebase auth if available (no redirect logic here)
        function initializeFirebaseAuth() {
            try {
                // Check if Firebase is properly configured
                if (!window.firebaseConfigured || !window.firebaseAuth || !window.firebaseDb) {
                    console.warn('Firebase not properly configured, using localStorage auth only');
                    return;
                }

                // Firebase is configured, use Firebase authentication
                window.firebaseAuthMethods.onAuthStateChanged(window.firebaseAuth, async (user) => {
                    if (!user) {
                        // Firebase user is not authenticated, but localStorage has session
                        // This is okay for now - user might have logged in before Firebase was configured
                        console.log('Firebase user not found, but localStorage session exists');
                        return;
                    }

                    // User is authenticated with Firebase, load their profile from Firestore
                    try {
                        const userDoc = await window.firebaseAuthMethods.getDoc(window.firebaseAuthMethods.doc(window.firebaseDb, 'users', user.uid));
                        if (userDoc.exists()) {
                            const userData = userDoc.data();
                            // Update localStorage with Firebase data
                            const currentUser = {
                                id: user.uid,
                                username: userData.username,
                                email: userData.email,
                                bio: userData.bio || 'New to the community',
                                followers: userData.followers || 0,
                                following: userData.following || 0,
                                stories: userData.stories || 0
                            };
                            localStorage.setItem('currentUser', JSON.stringify(currentUser));

                            // Update UI
                            if (window.updateUIForUser) {
                                window.updateUIForUser();
                            }
                        }
                    } catch (error) {
                        console.error('Error loading user data from Firestore:', error);
                    }
                });
            } catch (error) {
                console.error('Error initializing Firebase auth:', error);
            }
        }

        // Initialize Firebase auth after a delay (only for syncing, not for redirects)
        setTimeout(initializeFirebaseAuth, 500);
    </script>

    <!-- Strict authentication check -->
    <script>
        // Immediate authentication check - must run before any other scripts
        (function() {
            if (window.location.pathname === '/app') {
                const currentUser = localStorage.getItem('currentUser');
                console.log('Auth check - currentUser:', currentUser);

                if (!currentUser || currentUser === 'null' || currentUser === '') {
                    console.log('No valid user session found, redirecting to landing page');
                    window.location.replace('/');
                    return;
                }

                try {
                    const user = JSON.parse(currentUser);
                    if (!user || !user.username || !user.email) {
                        console.log('Invalid user data, redirecting to landing page');
                        localStorage.removeItem('currentUser');
                        window.location.replace('/');
                        return;
                    }
                    console.log('Valid user session found:', user.username);
                } catch (error) {
                    console.log('Error parsing user data, redirecting to landing page');
                    localStorage.removeItem('currentUser');
                    window.location.replace('/');
                    return;
                }
            }
        })();
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .lucide {
            width: 20px;
            height: 20px;
        }
        /* Simple spinner for loading states */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4f46e5;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* Styles for the modal */
        #commentModal.hidden {
            display: none;
        }
        /* Search results styling */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        /* Loading skeleton animations */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        /* Smooth transitions */
        .transition-all {
            transition: all 0.3s ease;
        }
        /* Hover effects */
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div id="app" class="min-h-screen">
        <!-- Top Navigation Bar -->
        <header class="bg-white/80 backdrop-blur-lg border-b border-slate-200 fixed top-0 left-0 right-0 z-20">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <a href="#" class="flex items-center gap-2 text-xl font-bold text-slate-900">
                        <i data-lucide="layers-3"></i>
                        <span>Narrate</span>
                    </a>

                    <!-- Search Bar -->
                    <div class="hidden md:block w-full max-w-md">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 lucide"></i>
                            <input type="text" id="searchInput" placeholder="Search stories, people, or topics..." class="w-full bg-slate-100 border border-transparent rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition">
                            <!-- Search Results Dropdown -->
                            <div id="searchResults" class="hidden absolute top-full left-0 right-0 mt-2 bg-white border border-slate-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center gap-2 md:gap-4">
                        <!-- Mobile Search Button -->
                        <button id="mobileSearchBtn" class="md:hidden p-2 rounded-full hover:bg-slate-100 transition">
                            <i data-lucide="search" class="text-slate-600"></i>
                        </button>

                        <button class="hidden md:flex items-center gap-2 bg-indigo-600 text-white font-semibold px-4 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">
                            <i data-lucide="pen-square"></i>
                            <span>Write</span>
                        </button>

                        <!-- Mobile Write Button -->
                        <button class="md:hidden p-2 rounded-full bg-indigo-600 text-white hover:bg-indigo-700 transition">
                            <i data-lucide="pen-square"></i>
                        </button>

                        <div class="hidden sm:block relative">
                            <button id="notificationBtn" class="p-2 rounded-full hover:bg-slate-100 transition relative">
                                <i data-lucide="bell" class="text-slate-600"></i>
                                <span id="notificationBadge" class="hidden absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">0</span>
                            </button>
                            <!-- Notification Dropdown -->
                            <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white border border-slate-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                                <div class="p-4 border-b border-slate-200 flex justify-between items-center">
                                    <h3 class="font-semibold text-slate-900">Notifications</h3>
                                    <button id="markAllReadBtn" class="text-sm text-indigo-600 hover:text-indigo-800">Mark all read</button>
                                </div>
                                <div id="notificationsList" class="divide-y divide-slate-100">
                                    <!-- Notifications will be loaded here -->
                                </div>
                            </div>
                        </div>
                        <button id="messagesBtn" class="hidden sm:block p-2 rounded-full hover:bg-slate-100 transition">
                            <i data-lucide="message-square" class="text-slate-600"></i>
                        </button>
                        <img id="userAvatar" src="https://placehold.co/40x40/E2E8F0/475569?text=JD" alt="User Avatar" class="w-8 h-8 md:w-10 md:h-10 rounded-full border-2 border-white cursor-pointer" onclick="toggleAuthModal()">
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Grid -->
        <main class="container mx-auto px-4 pt-24 pb-10">
            <!-- Grid updated for better responsiveness -->
            <div class="grid grid-cols-1 md:grid-cols-10 lg:grid-cols-12 gap-8">

                <!-- Left Column: Now visible on medium screens and up and is sticky -->
                <aside class="hidden md:block md:col-span-3 lg:col-span-3 space-y-6 self-start sticky top-24">
                    <!-- Profile Card -->
                    <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <div class="flex flex-col items-center text-center">
                            <img src="https://placehold.co/80x80/E2E8F0/475569?text=G" alt="Guest User" class="w-20 h-20 rounded-full mb-3 ring-4 ring-indigo-100">
                            <h2 class="text-xl font-bold text-slate-900">Guest User</h2>
                            <p class="text-sm text-slate-500 mt-1">Welcome to Narrate! Sign in to start sharing your stories.</p>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-center my-5">
                            <div>
                                <p class="font-bold text-lg">0</p>
                                <p class="text-xs text-slate-500">Stories</p>
                            </div>
                            <div>
                                <p class="font-bold text-lg">0</p>
                                <p class="text-xs text-slate-500">Followers</p>
                            </div>
                            <div>
                                <p class="font-bold text-lg">0</p>
                                <p class="text-xs text-slate-500">Following</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <span class="bg-slate-100 text-slate-500 text-xs font-semibold px-2.5 py-1 rounded-full">New User</span>
                        </div>
                    </div>
                    <!-- Navigation Links -->
                    <div class="bg-white p-3 rounded-xl border border-slate-200 shadow-sm">
                        <nav class="space-y-1">
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg bg-indigo-50 text-indigo-700 font-semibold">
                                <i data-lucide="layout-grid"></i> Feed
                            </a>
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="compass"></i> Explore
                            </a>
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="users"></i> Connect
                            </a>
                             <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="bookmark"></i> Bookmarks
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- Center Column: Spans adjusted for new breakpoints -->
                <div class="md:col-span-7 lg:col-span-6 space-y-6" id="mainFeed">
                    <!-- Create Post -->
                    <div class="bg-white p-4 rounded-xl border border-slate-200 shadow-sm">
                        <div class="flex items-start gap-4">
                            <img src="https://placehold.co/40x40/E2E8F0/475569?text=JD" alt="User Avatar" class="w-10 h-10 rounded-full">
                            <div class="w-full space-y-3">
                                <input type="text" id="storyTitleInput" placeholder="Your story's title..." class="w-full border-slate-300 rounded-lg text-lg font-semibold placeholder-slate-400 focus:ring-indigo-500 focus:border-indigo-500">
                                <textarea id="storyContentInput" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2 placeholder-slate-400" rows="3" placeholder="What's your story today, John?"></textarea>

                                <!-- Category Selection -->
                                <div class="flex items-center gap-3">
                                    <label class="text-sm font-medium text-slate-700">Category:</label>
                                    <select id="storyCategorySelect" class="border-slate-300 rounded-lg text-sm focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="Personal Experience">Personal Experience</option>
                                        <option value="Inspiration">Inspiration</option>
                                        <option value="Technology">Technology</option>
                                        <option value="Travel">Travel</option>
                                        <option value="Career">Career</option>
                                        <option value="Lifestyle">Lifestyle</option>
                                        <option value="Education">Education</option>
                                        <option value="Health">Health</option>
                                        <option value="Creative">Creative</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>

                                <div id="titleSuggestionsContainer" class="hidden space-y-2"></div>
                                <!-- Image Preview -->
                                <div id="imagePreview" class="hidden">
                                    <img id="previewImg" class="w-full h-48 object-cover rounded-lg border border-slate-200" alt="Preview">
                                    <button id="removeImageBtn" class="mt-2 text-sm text-red-600 hover:text-red-800">Remove image</button>
                                </div>

                                <div class="flex justify-between items-center pt-3 border-t border-slate-200">
                                    <div class="flex items-center gap-4">
                                        <input type="file" id="imageUpload" accept="image/*" class="hidden">
                                        <button id="imageUploadBtn" class="text-slate-500 hover:text-indigo-600 transition-colors" title="Add image">
                                            <i data-lucide="image"></i>
                                        </button>
                                        <button class="text-slate-500 hover:text-indigo-600 transition-colors" title="Add video (coming soon)">
                                            <i data-lucide="video"></i>
                                        </button>
                                        <button id="suggestTitlesBtn" class="hidden items-center gap-1.5 text-sm font-semibold text-indigo-600 hover:text-indigo-800">✨ Suggest Titles</button>
                                    </div>
                                    <button id="postBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Post</button>
                                </div>
                            </div>
                        </div>
                     </div>
                    <!-- Posts will be loaded here dynamically -->
                    <div id="postsContainer" class="space-y-6"></div>

                </div>

                <!-- Right Column: Visible on large screens, span adjusted and is sticky -->
                <aside class="hidden lg:block lg:col-span-3 space-y-6 self-start sticky top-24">
                    <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <h3 class="font-bold text-lg mb-4">People You May Know</h3>
                        <div id="suggestedUsers" class="space-y-4">
                            <!-- Suggested users will be loaded dynamically -->
                        </div>
                    </div>
                     <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <h3 class="font-bold text-lg mb-4">Recent Activity</h3>
                        <div class="text-center py-8">
                            <i data-lucide="activity" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                            <p class="text-slate-500 text-sm">No recent activity yet</p>
                            <p class="text-slate-400 text-xs mt-1">Activity will appear here as you interact with stories</p>
                        </div>
                    </div>
                </aside>

            </div>
        </main>
    </div>

    <!-- Comment Modal -->
    <div id="commentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-30 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
            <div class="p-5 border-b border-slate-200 flex justify-between items-center">
                <h3 class="text-lg font-bold">Comments</h3>
                <button id="closeModalBtn" class="p-1 rounded-full hover:bg-slate-100">
                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                </button>
            </div>

            <!-- Story Info -->
            <div class="p-5 border-b border-slate-200 bg-slate-50">
                <p class="text-sm text-slate-600">Commenting on: <span id="modalStoryTitle" class="font-semibold"></span></p>
            </div>

            <!-- Existing Comments -->
            <div class="flex-1 overflow-y-auto p-5">
                <div id="existingComments" class="space-y-4 mb-6">
                    <!-- Comments will be loaded here -->
                </div>
            </div>

            <!-- Add Comment Form -->
            <div class="p-5 border-t border-slate-200 space-y-4">
                <textarea id="commentTextarea" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2 placeholder-slate-400" rows="3" placeholder="Share your thoughts..."></textarea>
                <div id="commentError" class="text-sm text-red-500"></div>
                <div class="flex justify-between items-center">
                    <button id="generateCommentBtn" class="flex items-center gap-1.5 text-sm font-semibold text-indigo-600 hover:text-indigo-800">✨ Generate Comment</button>
                    <button id="postCommentBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Post Comment</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Authentication Modal (Hidden by default) -->
    <div id="authModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-30 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-5 border-b border-slate-200 flex justify-between items-center">
                <h3 id="authModalTitle" class="text-lg font-bold">Welcome to Narrate</h3>
                <button id="closeAuthModalBtn" class="p-1 rounded-full hover:bg-slate-100">
                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                </button>
            </div>

            <!-- Login Form -->
            <div id="loginForm" class="p-5 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <input type="email" id="loginEmail" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="<EMAIL>">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                    <input type="password" id="loginPassword" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="••••••••">
                </div>
                <div id="loginError" class="text-sm text-red-500"></div>
                <div class="flex flex-col gap-3">
                    <button id="loginBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Sign In</button>
                    <button id="showSignupBtn" class="text-indigo-600 font-medium hover:text-indigo-800">Don't have an account? Sign up</button>
                </div>
            </div>

            <!-- Signup Form (Hidden initially) -->
            <div id="signupForm" class="hidden p-5 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Username</label>
                    <input type="text" id="signupUsername" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="johndoe">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <input type="email" id="signupEmail" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="<EMAIL>">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                    <input type="password" id="signupPassword" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="••••••••">
                </div>
                <div id="signupError" class="text-sm text-red-500"></div>
                <div class="flex flex-col gap-3">
                    <button id="signupBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Create Account</button>
                    <button id="showLoginBtn" class="text-indigo-600 font-medium hover:text-indigo-800">Already have an account? Sign in</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Search Modal -->
    <div id="mobileSearchModal" class="hidden fixed inset-0 bg-white z-40 md:hidden">
        <div class="p-4 border-b border-slate-200">
            <div class="flex items-center gap-3">
                <button id="closeMobileSearchBtn" class="p-2 rounded-full hover:bg-slate-100">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-600"></i>
                </button>
                <div class="flex-1 relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 w-5 h-5"></i>
                    <input type="text" id="mobileSearchInput" placeholder="Search stories, people, or topics..." class="w-full bg-slate-100 border border-transparent rounded-full py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                </div>
            </div>
        </div>
        <div id="mobileSearchResults" class="flex-1 overflow-y-auto p-4">
            <!-- Mobile search results will appear here -->
        </div>
    </div>

    <!-- Messages Modal -->
    <div id="messagesModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-4xl h-[80vh] flex">
            <!-- Conversations List -->
            <div class="w-1/3 border-r border-slate-200 flex flex-col">
                <div class="p-4 border-b border-slate-200 flex justify-between items-center">
                    <h3 class="text-lg font-bold">Messages</h3>
                    <button id="closeMessagesBtn" class="p-1 rounded-full hover:bg-slate-100">
                        <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                    </button>
                </div>
                <div class="flex-1 overflow-y-auto">
                    <div id="conversationsList" class="divide-y divide-slate-100">
                        <!-- Conversations will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Chat Area -->
            <div class="flex-1 flex flex-col">
                <div id="chatHeader" class="hidden p-4 border-b border-slate-200 flex items-center gap-3">
                    <img id="chatPartnerAvatar" class="w-10 h-10 rounded-full" alt="">
                    <div>
                        <h4 id="chatPartnerName" class="font-semibold"></h4>
                        <p id="chatPartnerBio" class="text-sm text-slate-500"></p>
                    </div>
                </div>

                <div id="chatMessages" class="flex-1 overflow-y-auto p-4 space-y-3">
                    <div class="flex items-center justify-center h-full text-slate-500">
                        <div class="text-center">
                            <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-slate-300"></i>
                            <p>Select a conversation to start messaging</p>
                        </div>
                    </div>
                </div>

                <div id="chatInput" class="hidden p-4 border-t border-slate-200">
                    <div class="flex gap-3">
                        <input type="text" id="messageInput" placeholder="Type a message..." class="flex-1 border-slate-300 rounded-full px-4 py-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <button id="sendMessageBtn" class="bg-indigo-600 text-white p-2 rounded-full hover:bg-indigo-700 transition-colors">
                            <i data-lucide="send" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        document.addEventListener('DOMContentLoaded', () => {
            // --- Posts Data ---
            let mockPosts = [];

            // --- User Session Management ---
            let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;

            // Update UI based on login status
            window.updateUIForUser = function() {
                // Get current user from localStorage
                const currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
                const userAvatar = document.getElementById('userAvatar');
                const profileCard = document.querySelector('aside .bg-white');

                // Safety check - elements might not be loaded yet
                if (!userAvatar) return;

                if (currentUser) {
                    userAvatar.src = `https://placehold.co/40x40/E2E8F0/475569?text=${currentUser.username.substring(0, 2).toUpperCase()}`;
                    userAvatar.alt = currentUser.username;
                    if (profileCard) {
                        const h2Element = profileCard.querySelector('h2');
                        if (h2Element) {
                            h2Element.textContent = currentUser.username;
                        }
                    }
                } else {
                     userAvatar.src = `https://placehold.co/40x40/E2E8F0/475569?text=G`;
                     userAvatar.alt = "Guest";
                     if (profileCard) {
                        const h2Element = profileCard.querySelector('h2');
                        if (h2Element) {
                            h2Element.textContent = "Guest User";
                        }
                    }
                }
            }
            updateUIForUser();

            // --- Authentication Modal Functions ---
            const authModal = document.getElementById('authModal');
            const closeAuthModalBtn = document.getElementById('closeAuthModalBtn');
            const loginForm = document.getElementById('loginForm');
            const signupForm = document.getElementById('signupForm');
            const showSignupBtn = document.getElementById('showSignupBtn');
            const showLoginBtn = document.getElementById('showLoginBtn');
            const authModalTitle = document.getElementById('authModalTitle');

            window.toggleAuthModal = async function() {
                if (currentUser) {
                    if (confirm('Do you want to logout?')) {
                        try {
                            // Sign out from Firebase if configured
                            if (window.firebaseConfigured && window.firebaseAuth && window.firebaseAuthMethods) {
                                await window.firebaseAuthMethods.signOut(window.firebaseAuth);
                            }
                            // Clear localStorage
                            currentUser = null;
                            localStorage.removeItem('currentUser');
                            // Redirect to landing page
                            window.location.href = '/';
                        } catch (error) {
                            console.error('Error signing out:', error);
                            // Still clear localStorage and redirect even if Firebase signout fails
                            currentUser = null;
                            localStorage.removeItem('currentUser');
                            window.location.href = '/';
                        }
                    }
                } else {
                    // Redirect to landing page for login
                    window.location.href = '/';
                }
            };
            function showLoginForm() {
                loginForm.classList.remove('hidden');
                signupForm.classList.add('hidden');
                authModalTitle.textContent = 'Welcome Back';
            }
            function showSignupForm() {
                loginForm.classList.add('hidden');
                signupForm.classList.remove('hidden');
                authModalTitle.textContent = 'Join Narrate';
            }
            function closeAuthModal() {
                authModal.classList.add('hidden');
            }
            // Authentication modal is no longer used - users are redirected to landing page

            // --- Post Creation ---
            document.getElementById('postBtn').addEventListener('click', () => {
                const title = document.getElementById('storyTitleInput').value.trim();
                const content = document.getElementById('storyContentInput').value.trim();
                if (!currentUser) {
                    alert('Please login to create a post');
                    toggleAuthModal();
                    return;
                }
                if (!title || !content) {
                    alert('Please fill in both title and content');
                    return;
                }
                const newPost = {
                    id: Date.now(),
                    title,
                    content,
                    username: currentUser.username,
                    category: "General",
                    likes: 0,
                    comments: 0,
                    shares: 0,
                    createdAt: new Date()
                };
                mockPosts.unshift(newPost);
                loadPosts();
                document.getElementById('storyTitleInput').value = '';
                document.getElementById('storyContentInput').value = '';
                document.getElementById('titleSuggestionsContainer').classList.add('hidden');
            });

            // --- Load and Display Posts ---
            function loadPosts() {
                const postsContainer = document.getElementById('postsContainer');
                if (mockPosts.length === 0) {
                    postsContainer.innerHTML = `<div class="bg-white p-8 rounded-xl border border-slate-200 shadow-sm text-center"><p>No stories yet.</p></div>`;
                    return;
                }
                postsContainer.innerHTML = mockPosts.map(createPostHTML).join('');
                lucide.createIcons();
                attachCommentListeners();
            }

            function createPostHTML(post) {
                const timeAgo = getTimeAgo(new Date(post.createdAt));
                const userInitials = post.username.substring(0, 2).toUpperCase();
                return `
                    <article class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm hover-lift transition-all" data-story-title="${post.title}" data-story-content="${post.content}">
                        <div class="flex items-center mb-4">
                            <img src="https://placehold.co/48x48/E2E8F0/475569?text=${userInitials}" alt="${post.username}" class="w-12 h-12 rounded-full">
                            <div class="ml-4">
                                <h3 class="font-bold text-slate-900">${post.username}</h3>
                                <p class="text-sm text-slate-500">${timeAgo} &middot; <span class="font-semibold text-indigo-600">${post.category}</span></p>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">${post.title}</h2>
                        <p class="text-slate-600 leading-relaxed">${post.content}</p>
                        ${post.image ? `<img src="${post.image}" alt="Post image" class="w-full h-64 object-cover rounded-lg mt-4 border border-slate-200">` : ''}
                        <div class="mt-4 pt-4 border-t border-slate-200 flex justify-around">
                            <button class="flex items-center gap-2 text-slate-500 hover:text-red-500 font-medium transition-colors">
                                <i data-lucide="heart" class="w-5 h-5"></i> ${post.likes}
                            </button>
                            <button class="comment-btn flex items-center gap-2 text-slate-500 hover:text-sky-500 font-medium transition-colors">
                                <i data-lucide="message-circle" class="w-5 h-5"></i> ${post.comments}
                            </button>
                            <button class="flex items-center gap-2 text-slate-500 hover:text-emerald-500 font-medium transition-colors">
                                <i data-lucide="repeat-2" class="w-5 h-5"></i> ${post.shares}
                            </button>
                            <button class="flex items-center gap-2 text-slate-500 hover:text-indigo-500 font-medium transition-colors">
                                <i data-lucide="bookmark" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </article>
                `;
            }

            function getTimeAgo(date) {
                const now = new Date();
                const diffInSeconds = Math.floor((now - date) / 1000);
                if (diffInSeconds < 60) return 'Just now';
                if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
                if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
                return `${Math.floor(diffInSeconds / 86400)}d ago`;
            }

            loadPosts();

            // --- Load and Display Suggested Users ---
            async function loadSuggestedUsers() {
                const suggestedUsersContainer = document.getElementById('suggestedUsers');

                try {
                    const response = await fetch('/api/users');
                    const users = await response.json();

                    // Filter out current user and limit to 3 suggestions
                    const suggestions = users
                        .filter(user => !currentUser || user.id !== currentUser.id)
                        .slice(0, 3);

                    if (suggestions.length === 0) {
                        suggestedUsersContainer.innerHTML = `
                            <div class="text-center text-slate-500 text-sm">
                                <p>No users to suggest yet</p>
                            </div>
                        `;
                        return;
                    }

                    suggestedUsersContainer.innerHTML = suggestions.map(user => createUserSuggestionHTML(user)).join('');

                    // Attach follow button listeners
                    attachFollowListeners();

                } catch (error) {
                    console.error('Error loading suggested users:', error);
                    suggestedUsersContainer.innerHTML = `
                        <div class="text-center text-red-500 text-sm">
                            <p>Failed to load suggestions</p>
                        </div>
                    `;
                }
            }

            // --- Create User Suggestion HTML ---
            function createUserSuggestionHTML(user) {
                const userInitials = user.username.substring(0, 2).toUpperCase();
                const isFollowing = currentUser && currentUser.followingList && currentUser.followingList.includes(user.id);

                return `
                    <div class="flex items-center gap-3" data-user-id="${user.id}">
                        <img src="https://placehold.co/40x40/A5B4FC/312E81?text=${userInitials}" alt="${user.username}" class="w-10 h-10 rounded-full">
                        <div class="flex-grow">
                            <p class="font-semibold">${user.username}</p>
                            <p class="text-sm text-slate-500">${user.bio || 'New to the community'}</p>
                        </div>
                        <button class="follow-btn ${isFollowing ? 'bg-slate-200 text-slate-600' : 'bg-indigo-600 text-white'} font-semibold px-3 py-1 rounded-full text-sm hover:opacity-80 transition"
                                data-user-id="${user.id}">
                            ${isFollowing ? 'Following' : 'Follow'}
                        </button>
                    </div>
                `;
            }

            // --- Follow Functionality ---
            function attachFollowListeners() {
                document.querySelectorAll('.follow-btn').forEach(button => {
                    button.addEventListener('click', async (e) => {
                        if (!currentUser) {
                            alert('Please login to follow users');
                            toggleAuthModal();
                            return;
                        }

                        const targetUserId = button.dataset.userId;
                        const isCurrentlyFollowing = button.textContent.trim() === 'Following';

                        button.disabled = true;
                        button.textContent = isCurrentlyFollowing ? 'Unfollowing...' : 'Following...';

                        try {
                            const response = await fetch(`/api/users/${targetUserId}/follow`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ userId: currentUser.id })
                            });

                            const result = await response.json();

                            if (result.success) {
                                // Update button appearance
                                if (result.following) {
                                    button.textContent = 'Following';
                                    button.className = 'follow-btn bg-slate-200 text-slate-600 font-semibold px-3 py-1 rounded-full text-sm hover:opacity-80 transition';
                                } else {
                                    button.textContent = 'Follow';
                                    button.className = 'follow-btn bg-indigo-600 text-white font-semibold px-3 py-1 rounded-full text-sm hover:opacity-80 transition';
                                }

                                // Update current user's following count
                                currentUser.following = result.following_count;
                                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                                updateUIForUser();

                            } else {
                                alert('Failed to follow user: ' + (result.error || 'Unknown error'));
                                button.textContent = isCurrentlyFollowing ? 'Following' : 'Follow';
                            }
                        } catch (error) {
                            alert('Network error. Please try again.');
                            button.textContent = isCurrentlyFollowing ? 'Following' : 'Follow';
                        } finally {
                            button.disabled = false;
                        }
                    });
                });
            }

            loadSuggestedUsers();

            // --- Image Upload Functionality ---
            const imageUpload = document.getElementById('imageUpload');
            const imageUploadBtn = document.getElementById('imageUploadBtn');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            const removeImageBtn = document.getElementById('removeImageBtn');
            let selectedImage = null;

            imageUploadBtn.addEventListener('click', () => {
                imageUpload.click();
            });

            imageUpload.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('Image size must be less than 5MB');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        previewImg.src = e.target.result;
                        imagePreview.classList.remove('hidden');
                        selectedImage = file;
                    };
                    reader.readAsDataURL(file);
                }
            });

            removeImageBtn.addEventListener('click', () => {
                imagePreview.classList.add('hidden');
                imageUpload.value = '';
                selectedImage = null;
            });

            // --- Enhanced Post Creation ---
            const postBtn = document.getElementById('postBtn');
            const storyCategorySelect = document.getElementById('storyCategorySelect');

            postBtn.addEventListener('click', async () => {
                const title = document.getElementById('storyTitleInput').value.trim();
                const content = document.getElementById('storyContentInput').value.trim();
                const category = storyCategorySelect.value;

                if (!title || !content) {
                    alert('Please fill in both title and content');
                    return;
                }

                postBtn.disabled = true;
                postBtn.textContent = 'Posting...';

                try {
                    // For now, we'll create a mock post with the new features
                    // In a real implementation, you'd upload the image to a server first
                    const newPost = {
                        id: Date.now(),
                        title,
                        content,
                        category,
                        username: currentUser?.username || 'Anonymous',
                        likes: 0,
                        comments: 0,
                        shares: 0,
                        createdAt: new Date(),
                        image: selectedImage ? URL.createObjectURL(selectedImage) : null
                    };

                    mockPosts.unshift(newPost);
                    loadPosts();

                    // Clear the form
                    document.getElementById('storyTitleInput').value = '';
                    document.getElementById('storyContentInput').value = '';
                    document.getElementById('titleSuggestionsContainer').classList.add('hidden');
                    storyCategorySelect.value = 'Personal Experience';

                    // Clear image
                    if (selectedImage) {
                        removeImageBtn.click();
                    }

                    alert('Your story has been posted!');

                } catch (error) {
                    alert('Failed to post story. Please try again.');
                } finally {
                    postBtn.disabled = false;
                    postBtn.textContent = 'Post';
                }
            });

            // --- Search Functionality ---
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');
            let searchTimeout;

            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                if (query.length < 2) {
                    searchResults.classList.add('hidden');
                    return;
                }

                // Debounce search requests
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });

            // Hide search results when clicking outside
            document.addEventListener('click', (e) => {
                if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                    searchResults.classList.add('hidden');
                }
            });

            async function performSearch(query) {
                try {
                    searchResults.innerHTML = '<div class="p-4 text-slate-500 text-sm">Searching...</div>';
                    searchResults.classList.remove('hidden');

                    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                    const results = await response.json();

                    if (results.error) {
                        searchResults.innerHTML = `<div class="p-4 text-red-500 text-sm">${results.error}</div>`;
                        return;
                    }

                    const hasResults = (results.users && results.users.length > 0) || (results.posts && results.posts.length > 0);

                    if (!hasResults) {
                        searchResults.innerHTML = `
                            <div class="p-4 text-center">
                                <i data-lucide="search-x" class="w-8 h-8 text-slate-300 mx-auto mb-2"></i>
                                <p class="text-slate-500 text-sm">No results found for "${query}"</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    let html = '';

                    // Display user results
                    if (results.users && results.users.length > 0) {
                        html += '<div class="p-3 border-b border-slate-200"><h4 class="font-semibold text-slate-700 text-sm">People</h4></div>';
                        results.users.forEach(user => {
                            const userInitials = user.username.substring(0, 2).toUpperCase();
                            html += `
                                <div class="p-3 hover:bg-slate-50 cursor-pointer border-b border-slate-100" onclick="viewUserProfile('${user.id}')">
                                    <div class="flex items-center gap-3">
                                        <img src="https://placehold.co/32x32/E2E8F0/475569?text=${userInitials}" alt="${user.username}" class="w-8 h-8 rounded-full">
                                        <div>
                                            <p class="font-semibold text-sm">${user.username}</p>
                                            <p class="text-xs text-slate-500">${user.bio || 'New to the community'}</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }

                    // Display post results
                    if (results.posts && results.posts.length > 0) {
                        html += '<div class="p-3 border-b border-slate-200"><h4 class="font-semibold text-slate-700 text-sm">Stories</h4></div>';
                        results.posts.forEach(post => {
                            const timeAgo = getTimeAgo(new Date(post.createdAt));
                            html += `
                                <div class="p-3 hover:bg-slate-50 cursor-pointer border-b border-slate-100" onclick="viewPost('${post.id}')">
                                    <div>
                                        <p class="font-semibold text-sm line-clamp-1">${post.title}</p>
                                        <p class="text-xs text-slate-500 mb-1">by ${post.username} • ${timeAgo}</p>
                                        <p class="text-xs text-slate-600 line-clamp-2">${post.content}</p>
                                    </div>
                                </div>
                            `;
                        });
                    }

                    searchResults.innerHTML = html;
                    lucide.createIcons();

                } catch (error) {
                    console.error('Search error:', error);
                    searchResults.innerHTML = '<div class="p-4 text-red-500 text-sm">Search failed. Please try again.</div>';
                }
            }

            // Search result click handlers
            window.viewUserProfile = function(userId) {
                searchResults.classList.add('hidden');
                searchInput.value = '';
                alert(`User profile feature coming soon! User ID: ${userId}`);
            };

            window.viewPost = function(postId) {
                searchResults.classList.add('hidden');
                searchInput.value = '';
                // Scroll to the post if it's visible on the page
                const postElement = document.querySelector(`[data-post-id="${postId}"]`);
                if (postElement) {
                    postElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    postElement.classList.add('ring-2', 'ring-indigo-300');
                    setTimeout(() => {
                        postElement.classList.remove('ring-2', 'ring-indigo-300');
                    }, 3000);
                } else {
                    alert(`Post details feature coming soon! Post ID: ${postId}`);
                }
            };

            // --- Notifications System ---
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const notificationBadge = document.getElementById('notificationBadge');
            const notificationsList = document.getElementById('notificationsList');
            const markAllReadBtn = document.getElementById('markAllReadBtn');
            let unreadCount = 0;

            // Toggle notification dropdown
            notificationBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                notificationDropdown.classList.toggle('hidden');
                if (!notificationDropdown.classList.contains('hidden')) {
                    loadNotifications();
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!notificationBtn.contains(e.target) && !notificationDropdown.contains(e.target)) {
                    notificationDropdown.classList.add('hidden');
                }
            });

            // Mark all notifications as read
            markAllReadBtn.addEventListener('click', async () => {
                if (!currentUser) return;

                try {
                    const response = await fetch(`/api/notifications/${currentUser.id}/read-all`, {
                        method: 'PUT'
                    });

                    if (response.ok) {
                        loadNotifications();
                        updateNotificationBadge(0);
                    }
                } catch (error) {
                    console.error('Error marking notifications as read:', error);
                }
            });

            // Load notifications
            async function loadNotifications() {
                if (!currentUser) {
                    notificationsList.innerHTML = '<div class="p-4 text-center text-slate-500">Please login to see notifications</div>';
                    return;
                }

                try {
                    const response = await fetch(`/api/notifications/${currentUser.id}`);
                    const notifications = await response.json();

                    if (notifications.length === 0) {
                        notificationsList.innerHTML = `
                            <div class="p-8 text-center">
                                <i data-lucide="bell-off" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                                <p class="text-slate-500">No notifications yet</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    notificationsList.innerHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
                    lucide.createIcons();

                    // Update unread count
                    const unreadNotifications = notifications.filter(n => !n.read);
                    updateNotificationBadge(unreadNotifications.length);

                } catch (error) {
                    console.error('Error loading notifications:', error);
                    notificationsList.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load notifications</div>';
                }
            }

            // Create notification HTML
            function createNotificationHTML(notification) {
                const timeAgo = getTimeAgo(new Date(notification.createdAt));
                const isUnread = !notification.read;

                return `
                    <div class="p-4 hover:bg-slate-50 cursor-pointer ${isUnread ? 'bg-indigo-50' : ''}"
                         onclick="markNotificationRead('${notification.id}')">
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0">
                                ${getNotificationIcon(notification.type)}
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-slate-900 ${isUnread ? 'font-semibold' : ''}">${notification.message}</p>
                                <p class="text-xs text-slate-500 mt-1">${timeAgo}</p>
                            </div>
                            ${isUnread ? '<div class="w-2 h-2 bg-indigo-500 rounded-full flex-shrink-0 mt-2"></div>' : ''}
                        </div>
                    </div>
                `;
            }

            // Get notification icon based on type
            function getNotificationIcon(type) {
                const iconMap = {
                    'like': '<i data-lucide="heart" class="w-5 h-5 text-red-500"></i>',
                    'comment': '<i data-lucide="message-circle" class="w-5 h-5 text-blue-500"></i>',
                    'follow': '<i data-lucide="user-plus" class="w-5 h-5 text-green-500"></i>',
                    'mention': '<i data-lucide="at-sign" class="w-5 h-5 text-purple-500"></i>'
                };
                return iconMap[type] || '<i data-lucide="bell" class="w-5 h-5 text-slate-500"></i>';
            }

            // Mark notification as read
            window.markNotificationRead = async function(notificationId) {
                try {
                    const response = await fetch(`/api/notifications/${notificationId}/read`, {
                        method: 'PUT'
                    });

                    if (response.ok) {
                        loadNotifications();
                    }
                } catch (error) {
                    console.error('Error marking notification as read:', error);
                }
            };

            // Update notification badge
            function updateNotificationBadge(count) {
                unreadCount = count;
                if (count > 0) {
                    notificationBadge.textContent = count > 99 ? '99+' : count;
                    notificationBadge.classList.remove('hidden');
                } else {
                    notificationBadge.classList.add('hidden');
                }
            }

            // Load notifications periodically (every 30 seconds)
            setInterval(() => {
                if (currentUser && notificationDropdown.classList.contains('hidden')) {
                    // Only update badge when dropdown is closed to avoid disrupting user
                    fetch(`/api/notifications/${currentUser.id}`)
                        .then(response => response.json())
                        .then(notifications => {
                            const unreadNotifications = notifications.filter(n => !n.read);
                            updateNotificationBadge(unreadNotifications.length);
                        })
                        .catch(error => console.error('Error checking notifications:', error));
                }
            }, 30000);

            // --- Direct Messaging System ---
            const messagesBtn = document.getElementById('messagesBtn');
            const messagesModal = document.getElementById('messagesModal');
            const closeMessagesBtn = document.getElementById('closeMessagesBtn');
            const conversationsList = document.getElementById('conversationsList');
            const chatHeader = document.getElementById('chatHeader');
            const chatMessages = document.getElementById('chatMessages');
            const chatInput = document.getElementById('chatInput');
            const messageInput = document.getElementById('messageInput');
            const sendMessageBtn = document.getElementById('sendMessageBtn');
            let currentChatPartner = null;

            // Open messages modal
            messagesBtn.addEventListener('click', () => {
                if (!currentUser) {
                    alert('Please login to access messages');
                    toggleAuthModal();
                    return;
                }
                messagesModal.classList.remove('hidden');
                loadConversations();
            });

            // Close messages modal
            closeMessagesBtn.addEventListener('click', () => {
                messagesModal.classList.add('hidden');
                currentChatPartner = null;
                resetChatView();
            });

            // Load conversations
            async function loadConversations() {
                if (!currentUser) return;

                try {
                    conversationsList.innerHTML = '<div class="p-4 text-center text-slate-500">Loading...</div>';

                    const response = await fetch(`/api/conversations/${currentUser.id}`);
                    const conversations = await response.json();

                    if (conversations.length === 0) {
                        conversationsList.innerHTML = `
                            <div class="p-8 text-center">
                                <i data-lucide="message-circle-plus" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                                <p class="text-slate-500 text-sm">No conversations yet</p>
                                <p class="text-slate-400 text-xs">Start a conversation by visiting someone's profile</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    conversationsList.innerHTML = conversations.map(conversation => createConversationHTML(conversation)).join('');
                    lucide.createIcons();

                } catch (error) {
                    console.error('Error loading conversations:', error);
                    conversationsList.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load conversations</div>';
                }
            }

            // Create conversation HTML
            function createConversationHTML(conversation) {
                const partnerInitials = conversation.partnerName.substring(0, 2).toUpperCase();
                const timeAgo = getTimeAgo(new Date(conversation.lastMessage.createdAt));

                return `
                    <div class="p-4 hover:bg-slate-50 cursor-pointer conversation-item"
                         onclick="openChat('${conversation.partnerId}', '${conversation.partnerName}', '${conversation.partnerBio}')">
                        <div class="flex items-center gap-3">
                            <div class="relative">
                                <img src="https://placehold.co/40x40/E2E8F0/475569?text=${partnerInitials}"
                                     alt="${conversation.partnerName}" class="w-10 h-10 rounded-full">
                                ${conversation.unreadCount > 0 ? `<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">${conversation.unreadCount}</span>` : ''}
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex justify-between items-start">
                                    <h4 class="font-semibold text-sm truncate">${conversation.partnerName}</h4>
                                    <span class="text-xs text-slate-500">${timeAgo}</span>
                                </div>
                                <p class="text-sm text-slate-600 truncate">
                                    ${conversation.lastMessage.isFromMe ? 'You: ' : ''}${conversation.lastMessage.content}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Open chat with specific user
            window.openChat = async function(partnerId, partnerName, partnerBio) {
                currentChatPartner = partnerId;

                // Update chat header
                const partnerInitials = partnerName.substring(0, 2).toUpperCase();
                document.getElementById('chatPartnerAvatar').src = `https://placehold.co/40x40/E2E8F0/475569?text=${partnerInitials}`;
                document.getElementById('chatPartnerName').textContent = partnerName;
                document.getElementById('chatPartnerBio').textContent = partnerBio || 'New to the community';

                chatHeader.classList.remove('hidden');
                chatInput.classList.remove('hidden');

                // Load messages
                await loadChatMessages(partnerId);

                // Mark messages as read
                try {
                    await fetch(`/api/messages/${currentUser.id}/${partnerId}/read`, {
                        method: 'PUT'
                    });
                    loadConversations(); // Refresh to update unread counts
                } catch (error) {
                    console.error('Error marking messages as read:', error);
                }
            };

            // Load chat messages
            async function loadChatMessages(partnerId) {
                try {
                    const response = await fetch(`/api/messages/${currentUser.id}/${partnerId}`);
                    const messages = await response.json();

                    if (messages.length === 0) {
                        chatMessages.innerHTML = `
                            <div class="flex items-center justify-center h-full text-slate-500">
                                <div class="text-center">
                                    <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-slate-300"></i>
                                    <p>No messages yet. Start the conversation!</p>
                                </div>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    chatMessages.innerHTML = messages.map(message => createMessageHTML(message)).join('');
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                } catch (error) {
                    console.error('Error loading messages:', error);
                    chatMessages.innerHTML = '<div class="text-center text-red-500">Failed to load messages</div>';
                }
            }

            // Create message HTML
            function createMessageHTML(message) {
                const isFromMe = message.senderId === currentUser.id;
                const timeAgo = getTimeAgo(new Date(message.createdAt));

                return `
                    <div class="flex ${isFromMe ? 'justify-end' : 'justify-start'}">
                        <div class="max-w-xs lg:max-w-md">
                            <div class="px-4 py-2 rounded-lg ${isFromMe ? 'bg-indigo-600 text-white' : 'bg-slate-100 text-slate-900'}">
                                <p class="text-sm">${message.content}</p>
                            </div>
                            <p class="text-xs text-slate-500 mt-1 ${isFromMe ? 'text-right' : 'text-left'}">${timeAgo}</p>
                        </div>
                    </div>
                `;
            }

            // Send message
            async function sendMessage() {
                if (!currentChatPartner || !currentUser) return;

                const content = messageInput.value.trim();
                if (!content) return;

                messageInput.disabled = true;
                sendMessageBtn.disabled = true;

                try {
                    const response = await fetch('/api/messages', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            senderId: currentUser.id,
                            receiverId: currentChatPartner,
                            content: content
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        messageInput.value = '';
                        await loadChatMessages(currentChatPartner);
                        loadConversations(); // Refresh conversations list
                    } else {
                        alert('Failed to send message: ' + (result.error || 'Unknown error'));
                    }

                } catch (error) {
                    console.error('Error sending message:', error);
                    alert('Network error. Please try again.');
                } finally {
                    messageInput.disabled = false;
                    sendMessageBtn.disabled = false;
                }
            }

            // Send message on button click
            sendMessageBtn.addEventListener('click', sendMessage);

            // Send message on Enter key
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Reset chat view
            function resetChatView() {
                chatHeader.classList.add('hidden');
                chatInput.classList.add('hidden');
                chatMessages.innerHTML = `
                    <div class="flex items-center justify-center h-full text-slate-500">
                        <div class="text-center">
                            <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-slate-300"></i>
                            <p>Select a conversation to start messaging</p>
                        </div>
                    </div>
                `;
                lucide.createIcons();
            }

            // --- Mobile Search Functionality ---
            const mobileSearchBtn = document.getElementById('mobileSearchBtn');
            const mobileSearchModal = document.getElementById('mobileSearchModal');
            const closeMobileSearchBtn = document.getElementById('closeMobileSearchBtn');
            const mobileSearchInput = document.getElementById('mobileSearchInput');
            const mobileSearchResults = document.getElementById('mobileSearchResults');
            let mobileSearchTimeout;

            mobileSearchBtn.addEventListener('click', () => {
                mobileSearchModal.classList.remove('hidden');
                mobileSearchInput.focus();
            });

            closeMobileSearchBtn.addEventListener('click', () => {
                mobileSearchModal.classList.add('hidden');
                mobileSearchInput.value = '';
                mobileSearchResults.innerHTML = '';
            });

            mobileSearchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();

                clearTimeout(mobileSearchTimeout);

                if (query.length < 2) {
                    mobileSearchResults.innerHTML = '';
                    return;
                }

                mobileSearchTimeout = setTimeout(() => {
                    performMobileSearch(query);
                }, 300);
            });

            async function performMobileSearch(query) {
                try {
                    mobileSearchResults.innerHTML = '<div class="p-4 text-slate-500 text-sm">Searching...</div>';

                    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                    const results = await response.json();

                    if (results.error) {
                        mobileSearchResults.innerHTML = `<div class="p-4 text-red-500 text-sm">${results.error}</div>`;
                        return;
                    }

                    const hasResults = (results.users && results.users.length > 0) || (results.posts && results.posts.length > 0);

                    if (!hasResults) {
                        mobileSearchResults.innerHTML = `
                            <div class="p-8 text-center">
                                <i data-lucide="search-x" class="w-12 h-12 text-slate-300 mx-auto mb-4"></i>
                                <p class="text-slate-500">No results found for "${query}"</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    let html = '';

                    // Display user results
                    if (results.users && results.users.length > 0) {
                        html += '<div class="mb-6"><h4 class="font-semibold text-slate-700 mb-3">People</h4>';
                        results.users.forEach(user => {
                            const userInitials = user.username.substring(0, 2).toUpperCase();
                            html += `
                                <div class="p-3 hover:bg-slate-50 rounded-lg cursor-pointer mb-2" onclick="viewUserProfile('${user.id}')">
                                    <div class="flex items-center gap-3">
                                        <img src="https://placehold.co/40x40/E2E8F0/475569?text=${userInitials}" alt="${user.username}" class="w-10 h-10 rounded-full">
                                        <div>
                                            <p class="font-semibold">${user.username}</p>
                                            <p class="text-sm text-slate-500">${user.bio || 'New to the community'}</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }

                    // Display post results
                    if (results.posts && results.posts.length > 0) {
                        html += '<div><h4 class="font-semibold text-slate-700 mb-3">Stories</h4>';
                        results.posts.forEach(post => {
                            const timeAgo = getTimeAgo(new Date(post.createdAt));
                            html += `
                                <div class="p-3 hover:bg-slate-50 rounded-lg cursor-pointer mb-2" onclick="viewPost('${post.id}')">
                                    <div>
                                        <p class="font-semibold line-clamp-1">${post.title}</p>
                                        <p class="text-sm text-slate-500 mb-1">by ${post.username} • ${timeAgo}</p>
                                        <p class="text-sm text-slate-600 line-clamp-2">${post.content}</p>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }

                    mobileSearchResults.innerHTML = html;
                    lucide.createIcons();

                } catch (error) {
                    console.error('Mobile search error:', error);
                    mobileSearchResults.innerHTML = '<div class="p-4 text-red-500 text-sm">Search failed. Please try again.</div>';
                }
            }

            // Gemini API Key - LEAVE BLANK
            const API_KEY = "";

            // --- ✨ AI Story Title Suggester ---
            const storyContentInput = document.getElementById('storyContentInput');
            const suggestTitlesBtn = document.getElementById('suggestTitlesBtn');
            const titleSuggestionsContainer = document.getElementById('titleSuggestionsContainer');
            const storyTitleInput = document.getElementById('storyTitleInput');

            storyContentInput.addEventListener('input', () => {
                suggestTitlesBtn.classList.toggle('hidden', storyContentInput.value.trim().length <= 20);
                suggestTitlesBtn.classList.toggle('flex', storyContentInput.value.trim().length > 20);
            });

            suggestTitlesBtn.addEventListener('click', async () => {
                const storyText = storyContentInput.value;
                if (storyText.length < 20 || !API_KEY) {
                    if(!API_KEY) titleSuggestionsContainer.innerHTML = `<p class="text-sm text-red-500">API Key is not set.</p>`;
                    return;
                };

                suggestTitlesBtn.disabled = true;
                suggestTitlesBtn.innerHTML = '<div class="spinner"></div>';
                titleSuggestionsContainer.classList.remove('hidden');
                titleSuggestionsContainer.innerHTML = '<p class="text-sm text-slate-500">✨ Thinking of some titles...</p>';

                const prompt = `Based on the following story draft, generate 5 creative and engaging title suggestions. Return as a JSON object with a "titles" array. Story: "${storyText}"`;
                const payload = {
                  contents: [{ role: "user", parts: [{ text: prompt }] }],
                  generationConfig: {
                    responseMimeType: "application/json",
                    responseSchema: { type: "OBJECT", properties: { titles: { type: "ARRAY", items: { type: "STRING" } } } }
                  }
                };

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    const parsedJson = JSON.parse(result.candidates[0].content.parts[0].text);

                    titleSuggestionsContainer.innerHTML = '';
                    parsedJson.titles.forEach(title => {
                        const btn = document.createElement('button');
                        btn.className = 'w-full text-left p-2 bg-slate-100 rounded-md hover:bg-indigo-100 text-slate-700 text-sm';
                        btn.textContent = title;
                        btn.onclick = () => {
                            storyTitleInput.value = title;
                            titleSuggestionsContainer.classList.add('hidden');
                        };
                        titleSuggestionsContainer.appendChild(btn);
                    });
                } catch (error) {
                    titleSuggestionsContainer.innerHTML = `<p class="text-sm text-red-500">Could not fetch suggestions. ${error.message}</p>`;
                } finally {
                    suggestTitlesBtn.disabled = false;
                    suggestTitlesBtn.innerHTML = '✨ Suggest Titles';
                }
            });

            // --- ✨ AI Bio Generator ---
            document.querySelectorAll('.generate-bio-btn').forEach(button => {
                button.addEventListener('click', async (e) => {
                    if (!API_KEY) return;
                    const card = e.currentTarget.closest('[data-person-name]');
                    const name = card.dataset.personName;
                    const role = card.dataset.personRole;
                    const bioTextElement = card.querySelector('.bio-text');
                    const originalText = bioTextElement.textContent;
                    button.disabled = true;
                    button.innerHTML = '<div class="spinner !w-5 !h-5"></div>';

                    const prompt = `Generate a short, one-sentence, friendly, and hypothetical bio for a person named ${name} who is a ${role}. Make it sound like a profile summary on a social network.`;
                    const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                    try {
                         const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });
                        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                        const result = await response.json();
                        bioTextElement.textContent = result.candidates[0].content.parts[0].text;
                        button.classList.add('hidden');
                    } catch (error) {
                        bioTextElement.textContent = originalText;
                        button.disabled = false;
                        button.innerHTML = '✨';
                    }
                });
            });

            // --- Comment Modal & ✨ AI Comment Generator ---
            const modal = document.getElementById('commentModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const modalStoryTitle = document.getElementById('modalStoryTitle');
            const commentTextarea = document.getElementById('commentTextarea');
            const generateCommentBtn = document.getElementById('generateCommentBtn');
            const commentError = document.getElementById('commentError');

            function attachCommentListeners() {
                document.querySelectorAll('.comment-btn').forEach(button => {
                    button.addEventListener('click', async (e) => {
                        const article = e.currentTarget.closest('article');
                        const title = article.dataset.storyTitle;
                        const content = article.dataset.storyContent;

                        // Get post ID from the like button
                        const likeBtn = article.querySelector('.like-btn');
                        const postId = likeBtn ? likeBtn.dataset.postId : null;

                        modalStoryTitle.textContent = title;
                        modal.dataset.storyTitle = title;
                        modal.dataset.storyContent = content;
                        modal.dataset.postId = postId;
                        commentTextarea.value = '';
                        commentError.textContent = '';

                        // Load existing comments
                        await loadComments(postId);

                        modal.classList.remove('hidden');
                    });
                });
            }

            // --- Load Comments Function ---
            async function loadComments(postId) {
                const existingCommentsContainer = document.getElementById('existingComments');

                if (!postId) {
                    existingCommentsContainer.innerHTML = '<p class="text-slate-500 text-sm">Unable to load comments</p>';
                    return;
                }

                try {
                    existingCommentsContainer.innerHTML = '<p class="text-slate-500 text-sm">Loading comments...</p>';

                    const response = await fetch(`/api/posts/${postId}/comments`);
                    const comments = await response.json();

                    if (comments.length === 0) {
                        existingCommentsContainer.innerHTML = `
                            <div class="text-center py-8">
                                <i data-lucide="message-circle" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                                <p class="text-slate-500">No comments yet</p>
                                <p class="text-slate-400 text-sm">Be the first to share your thoughts!</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    existingCommentsContainer.innerHTML = comments.map(comment => createCommentHTML(comment)).join('');
                    lucide.createIcons();

                } catch (error) {
                    console.error('Error loading comments:', error);
                    existingCommentsContainer.innerHTML = '<p class="text-red-500 text-sm">Failed to load comments</p>';
                }
            }

            // --- Create Comment HTML ---
            function createCommentHTML(comment) {
                const timeAgo = getTimeAgo(new Date(comment.createdAt));
                const userInitials = comment.username.substring(0, 2).toUpperCase();

                return `
                    <div class="flex gap-3 p-3 bg-slate-50 rounded-lg">
                        <img src="https://placehold.co/32x32/E2E8F0/475569?text=${userInitials}" alt="${comment.username}" class="w-8 h-8 rounded-full flex-shrink-0">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <span class="font-semibold text-sm">${comment.username}</span>
                                <span class="text-xs text-slate-500">${timeAgo}</span>
                            </div>
                            <p class="text-slate-700 text-sm leading-relaxed">${comment.content}</p>
                        </div>
                    </div>
                `;
            }

            const closeModal = () => modal.classList.add('hidden');
            closeModalBtn.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => e.target === modal && closeModal());

            generateCommentBtn.addEventListener('click', async () => {
                if (!API_KEY) {
                    commentError.textContent = 'API Key is not set.';
                    return;
                }
                const title = modal.dataset.storyTitle;
                const content = modal.dataset.storyContent;
                generateCommentBtn.disabled = true;
                generateCommentBtn.innerHTML = '<div class="spinner"></div> Generating...';
                commentError.textContent = '';

                const prompt = `Generate a short, insightful, and friendly comment for a story titled "${title}". The story content is: "${content}"`;
                const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    commentTextarea.value = result.candidates[0].content.parts[0].text;
                } catch (error) {
                    commentError.textContent = `Failed to generate comment. ${error.message}`;
                } finally {
                    generateCommentBtn.disabled = false;
                    generateCommentBtn.innerHTML = '✨ Generate Comment';
                }
            });

            // --- Post Comment Functionality ---
            const postCommentBtn = document.getElementById('postCommentBtn');

            postCommentBtn.addEventListener('click', async () => {
                if (!currentUser) {
                    alert('Please login to comment');
                    toggleAuthModal();
                    return;
                }

                const content = commentTextarea.value.trim();
                if (!content) {
                    commentError.textContent = 'Please enter a comment';
                    return;
                }

                const postId = modal.dataset.postId;
                if (!postId) {
                    commentError.textContent = 'Could not identify the post';
                    return;
                }

                postCommentBtn.disabled = true;
                postCommentBtn.textContent = 'Posting...';
                commentError.textContent = '';

                try {
                    const response = await fetch(`/api/posts/${postId}/comment`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            userId: currentUser.id,
                            username: currentUser.username,
                            content: content
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Clear the form
                        commentTextarea.value = '';

                        // Reload comments to show the new one
                        await loadComments(postId);

                        // Update the comment count in the main feed
                        const article = document.querySelector(`[data-story-title="${modal.dataset.storyTitle}"]`);
                        if (article) {
                            const commentBtn = article.querySelector('.comment-btn');
                            if (commentBtn) {
                                const commentText = commentBtn.innerHTML;
                                commentBtn.innerHTML = commentText.replace(/\d+/, result.comments);
                            }
                        }

                    } else {
                        commentError.textContent = result.error || 'Failed to post comment';
                    }
                } catch (error) {
                    commentError.textContent = 'Network error. Please try again.';
                } finally {
                    postCommentBtn.disabled = false;
                    postCommentBtn.textContent = 'Post Comment';
                }
            });
        });
    </script>
</body>
</html>
